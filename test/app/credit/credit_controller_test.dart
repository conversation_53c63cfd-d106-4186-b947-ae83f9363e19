import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:td_procurement/app/credit/domain/entities/credit_statement_params.dart';
import 'package:td_procurement/app/credit/presentation/controllers/credit_controller.dart';
import 'package:td_procurement/app/credit/presentation/controllers/credit_state.dart';

void main() {
  group('CreditController Tests', () {
    late ProviderContainer container;

    setUp(() {
      container = ProviderContainer();
    });

    tearDown(() {
      container.dispose();
    });

    test('should initialize with correct initial state', () {
      final creditState = container.read(creditControllerProvider);
      
      expect(creditState, isA<CreditState>());
      expect(creditState.creditStatement.hasValue, isTrue);
      expect(creditState.creditStatement.value, isNull);
      expect(creditState.repayments.hasValue, isTrue);
      expect(creditState.repayments.value, isEmpty);
      expect(creditState.creditStatementParams, isNull);
    });

    test('should update payment type correctly', () {
      final controller = container.read(creditControllerProvider.notifier);
      
      // Test updating to debits (Total Repayment)
      controller.updatePaymentType(CreditPaymentType.debits);
      
      final state = container.read(creditControllerProvider);
      expect(state.creditStatementParams?.paymentType, equals(CreditPaymentType.debits));
    });

    test('should update payment type to credits correctly', () {
      final controller = container.read(creditControllerProvider.notifier);
      
      // Test updating to credits (Total Credit)
      controller.updatePaymentType(CreditPaymentType.credits);
      
      final state = container.read(creditControllerProvider);
      expect(state.creditStatementParams?.paymentType, equals(CreditPaymentType.credits));
    });

    test('should update payment type to balance correctly', () {
      final controller = container.read(creditControllerProvider.notifier);
      
      // Test updating to balance
      controller.updatePaymentType(CreditPaymentType.balance);
      
      final state = container.read(creditControllerProvider);
      expect(state.creditStatementParams?.paymentType, equals(CreditPaymentType.balance));
    });

    test('should maintain backward compatibility with creditStatementArgProvider', () {
      final controller = container.read(creditControllerProvider.notifier);
      
      // Update payment type
      controller.updatePaymentType(CreditPaymentType.debits);
      
      // Check that the legacy provider is also updated
      final legacyParams = container.read(creditStatementArgProvider);
      expect(legacyParams?.paymentType, equals(CreditPaymentType.debits));
    });

    test('should handle date filter updates', () {
      final controller = container.read(creditControllerProvider.notifier);
      final startDate = DateTime(2024, 1, 1);
      final endDate = DateTime(2024, 12, 31);
      
      // First set a payment type
      controller.updatePaymentType(CreditPaymentType.balance);
      
      // Then update date filter
      controller.updateDateFilter(startDate, endDate);
      
      final state = container.read(creditControllerProvider);
      expect(state.creditStatementParams?.startDate, equals(startDate));
      expect(state.creditStatementParams?.endDate, equals(endDate));
      expect(state.creditStatementParams?.paymentType, equals(CreditPaymentType.balance));
    });

    test('should handle pagination correctly', () {
      final controller = container.read(creditControllerProvider.notifier);
      
      // First set a payment type
      controller.updatePaymentType(CreditPaymentType.balance);
      
      // Then paginate
      controller.paginateData(2);
      
      final state = container.read(creditControllerProvider);
      expect(state.creditStatementParams?.batch, equals(2));
      expect(state.creditStatementParams?.paymentType, equals(CreditPaymentType.balance));
    });
  });

  group('CreditPaymentType Tests', () {
    test('should have correct titles', () {
      expect(CreditPaymentType.balance.title, equals('Balance'));
      expect(CreditPaymentType.credits.title, equals('Total Credit'));
      expect(CreditPaymentType.debits.title, equals('Total Repayment'));
    });

    test('should have correct colors', () {
      expect(CreditPaymentType.balance.color.value, equals(0xFF081F24));
      expect(CreditPaymentType.credits.color.value, equals(0xFF0CA653));
      expect(CreditPaymentType.debits.color.value, equals(0xFFE61010));
    });
  });
}
