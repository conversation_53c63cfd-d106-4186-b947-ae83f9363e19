import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:gap/gap.dart';
import 'package:table_calendar/table_calendar.dart';
import 'package:td_procurement/src/extensions/date_time.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';

class MultiDatePicker extends ConsumerStatefulWidget {
  final void Function(DateTime?, DateTime?) onSelectRange;
  final VoidCallback onCancel;
  final DateTime? initialDate;
  final DateTime? selectedStartDate;
  final DateTime? selectedEndDate;

  const MultiDatePicker({
    super.key,
    required this.onSelectRange,
    required this.onCancel,
    this.initialDate,
    this.selectedStartDate,
    this.selectedEndDate,
  });

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => MultiDatePickerState();
}

class MultiDatePickerState extends ConsumerState<MultiDatePicker> {
  late DateTime? _selectedStartDate = widget.selectedStartDate;
  late DateTime? _selectedEndDate = widget.selectedEndDate;

  late DateTime _focusedLeftMonth = _selectedStartDate != null
      ? (_selectedStartDate!.month == DateTime.now().month
          ? DateTime(DateTime.now().year, DateTime.now().month - 1)
          : _selectedStartDate!)
      : DateTime(DateTime.now().year, DateTime.now().month - 1);

  late DateTime _focusedRightMonth = _selectedStartDate != null
      ? (_selectedStartDate!.month == DateTime.now().month
          ? DateTime(DateTime.now().year, DateTime.now().month)
          : DateTime(_focusedLeftMonth.year, _focusedLeftMonth.month + 1))
      : DateTime(DateTime.now().year, DateTime.now().month);

  // late DateTime _focusedLeftMonth = _selectedStartDate != null
  //     ? _selectedStartDate!.month == DateTime.now().month
  //         ? _selectedStartDate!.subtract(const Duration(days: 30))
  //         : _selectedStartDate!
  //     : DateTime.now().subtract(const Duration(days: 30));
  // late DateTime _focusedRightMonth = _selectedStartDate != null
  //     ? _selectedStartDate!.month == DateTime.now().month
  //         ? _selectedStartDate!
  //         : _selectedStartDate!.add(const Duration(days: 30))
  //     : DateTime.now();

  // late DateTime _focusedLeftMonth = _selectedStartDate != null
  //     ? _selectedStartDate!
  //     : DateTime.now().subtract(const Duration(days: 30));
  // late DateTime _focusedRightMonth = _selectedStartDate != null
  //     ? _selectedStartDate!.add(const Duration(days: 30))
  //     : DateTime.now();

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Flexible(
              child: _buildCalendar(_focusedLeftMonth, isLeftCalendar: true),
            ),
            const Gap(16),
            Flexible(
              child: _buildCalendar(_focusedRightMonth, isLeftCalendar: false),
            ),
          ],
        ),
        const Spacer(),
        Container(
          padding: const EdgeInsets.only(top: 15),
          decoration: BoxDecoration(
              border: Border(top: BorderSide(color: Palette.stroke))),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              ElevatedButton(
                onPressed: widget.onCancel,
                style: ElevatedButton.styleFrom(
                  elevation: 0,
                  foregroundColor: Palette.primary,
                  backgroundColor: Palette.primary.withOpacity(0.2),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: const Text('Cancel'),
              ),
              const Gap(20),
              ElevatedButton(
                onPressed: () {
                  widget.onSelectRange(_selectedStartDate, _selectedEndDate);
                },
                style: ElevatedButton.styleFrom(
                  elevation: 0,
                  foregroundColor: Colors.white,
                  backgroundColor: Palette.primaryBlack,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: const Text('Save'),
              ),
            ],
          ),
        ),
      ],
    );
  }

  void _moveCalendars(bool moveBackwards) {
    setState(() {
      if (moveBackwards) {
        _focusedLeftMonth =
            DateTime(_focusedLeftMonth.year, _focusedLeftMonth.month - 1, 1);
        _focusedRightMonth =
            DateTime(_focusedRightMonth.year, _focusedRightMonth.month - 1, 1);
      } else if (_focusedRightMonth.isBefore(DateTime.now())) {
        _focusedLeftMonth =
            DateTime(_focusedLeftMonth.year, _focusedLeftMonth.month + 1, 1);
        _focusedRightMonth =
            DateTime(_focusedRightMonth.year, _focusedRightMonth.month + 1, 1);
      }
    });
  }

  Widget _buildCalendar(DateTime focusedMonth, {required bool isLeftCalendar}) {
    final now = DateTime.now();
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            if (isLeftCalendar)
              IconButton(
                icon: const Icon(Icons.arrow_back),
                onPressed: () => _moveCalendars(true),
              )
            else
              const Gap(48),
            Text(
              '${focusedMonth.toMonthName()} ${focusedMonth.year}',
              style: const TextStyle(fontWeight: FontWeight.bold),
            ),
            if (!isLeftCalendar)
              IconButton(
                icon: const Icon(Icons.arrow_forward),
                onPressed: _focusedRightMonth
                        .isBefore(DateTime(now.year, now.month, 1))
                    ? () => _moveCalendars(false)
                    : null, // Disable when right calendar reaches the current month
              )
            else
              const Gap(48),
          ],
        ),
        const Gap(10),
        TableCalendar(
          firstDay: DateTime.now().subtract(const Duration(days: 365 * 5)),
          lastDay: DateTime.now().add(const Duration(days: 365)),
          focusedDay: focusedMonth,
          rowHeight: 45.0,
          daysOfWeekHeight: 16.0,
          enabledDayPredicate: (day) {
            if (!isLeftCalendar && day.isAfter(now)) {
              return false; // Disable future dates on the right calendar
            }
            return true;
          },
          selectedDayPredicate: (day) {
            if (_selectedStartDate != null && _selectedEndDate != null) {
              return day.isAfter(_selectedStartDate!) &&
                  day.isBefore(_selectedEndDate!);
            }
            return false;
          },
          rangeStartDay: _selectedStartDate,
          rangeEndDay: _selectedEndDate,
          onDaySelected: (selectedDay, _) {
            setState(() {
              if (_selectedStartDate == null || _selectedEndDate != null) {
                _selectedStartDate = selectedDay;
                _selectedEndDate = null;
              } else {
                _selectedEndDate = selectedDay.isAfter(_selectedStartDate!)
                    ? selectedDay
                    : _selectedStartDate;
                _selectedStartDate = selectedDay.isAfter(_selectedStartDate!)
                    ? _selectedStartDate
                    : selectedDay;
              }
            });
          },
          calendarFormat: CalendarFormat.month,
          startingDayOfWeek: StartingDayOfWeek.sunday,
          headerVisible: false,
          calendarStyle: CalendarStyle(
            outsideDaysVisible: false,
            cellMargin: const EdgeInsets.all(1),
            rangeHighlightColor: Colors.blue.withValues(alpha: 0.5),
            selectedDecoration: BoxDecoration(
              color: Colors.grey.withValues(alpha: 0.1),
              shape: BoxShape.circle,
            ),
            rangeStartDecoration: const BoxDecoration(
              color: Colors.blue,
              shape: BoxShape.circle,
            ),
            rangeEndDecoration: const BoxDecoration(
              color: Colors.blue,
              shape: BoxShape.circle,
            ),
          ),
        ),
      ],
    );
  }
}
