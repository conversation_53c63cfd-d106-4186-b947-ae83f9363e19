import 'package:dio/dio.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:td_flutter_core/services/api/td_api.dart';
import 'package:td_procurement/app/credit/data/models/balance.dart';
import 'package:td_procurement/app/credit/data/models/credit_statement.dart';
import 'package:td_procurement/app/credit/data/models/loan.dart';
import 'package:td_procurement/app/credit/data/models/repayment.dart';
import 'package:td_procurement/app/credit/domain/entities/credit_statement_params.dart';
import 'package:td_procurement/core/DI/di_providers.dart';
import 'package:td_procurement/core/config/app_config/app_config.dart';
import 'package:td_procurement/core/models/pagination.dart';
import 'package:td_procurement/core/services/api/api_paths.dart';

class CreditDataSourceImplementation extends CreditDataSource {
  final TdApiClient _apiClient;
  final AppConfig _config;
  CreditDataSourceImplementation(this._apiClient, this._config);

  @override
  Future<CreditStatement> fetchCreditStatement(
      CreditStatementParams params) async {
    // Make both requests in parallel
    final results = await Future.wait([
      _apiClient.get(
        "${_config.consoleUrl}$kLoansApiPath",
        queryParameters: params.toMap(),
      ),
      fetchLoanBalance(),
    ]);

    final res = results[0] as Response<dynamic>;
    final balanceData = results[1] as LoanBalance;
    final loansData = res.data['data'];

    return CreditStatement(
      loans: (loansData['loandraws'] as List<dynamic>)
          .map((e) => Loan.fromMap(e as Map<String, dynamic>))
          .toList(),
      balance: balanceData.balance,
      totalCredits: 0, // totalCredits
      totalDebits: 0, // totalDebits
      totalCount:
          (loansData['loandraws'] as List<dynamic>).length, // totalCount
      pagination: loansData['pagination'] != null
          ? Pagination.fromMap(loansData['pagination'] as Map<String, dynamic>)
          : Pagination(page: 1, perPage: 10, totalPages: 1),
      isCompleted: true,
    );
  }

  @override
  Future<LoanBalance> fetchLoanBalance() async {
    final res = await _apiClient.get(
      "${_config.consoleUrl}$kLoanBalanceApiPath",
    );
    return LoanBalance.fromMap(res.data['data']);
  }

  @override
  Future<bool> sendCreditStatement(CreditStatementParams params) async {
    await _apiClient.get(
      "${_config.awsApiUrlV3}$kLoansApiPath",
      queryParameters: params.toMap(),
    );
    return true;
  }

  @override
  Future<List<LoanRepayment>> fetchRepayments(
      CreditStatementParams params) async {
    final res = await _apiClient.get(
      "${_config.consoleUrl}$kRepaymentsApiPath",
      queryParameters: params.toMap(),
    );
    final repaymentsData = res.data['data'];
    return (repaymentsData['repayments'] as List<dynamic>)
        .map((e) => LoanRepayment.fromMap(e as Map<String, dynamic>))
        .toList();
  }
}

abstract class CreditDataSource {
  Future<CreditStatement> fetchCreditStatement(CreditStatementParams params);
  Future<LoanBalance> fetchLoanBalance();
  Future<bool> sendCreditStatement(CreditStatementParams params);
  Future<List<LoanRepayment>> fetchRepayments(CreditStatementParams params);
}

final creditDataProvider = Provider<CreditDataSource>((ref) {
  final apiClient = ref.read(apiClientProvider);
  final config = ref.read(appConfigProvider);
  return CreditDataSourceImplementation(apiClient, config);
});
