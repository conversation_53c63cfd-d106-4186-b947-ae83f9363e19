// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:convert';

class LoanBalance {
  final num balance;

  LoanBalance(
    this.balance,
  );

  LoanBalance copyWith({
    num? balance,
  }) {
    return LoanBalance(
      balance ?? this.balance,
    );
  }

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'balance': balance,
    };
  }

  factory LoanBalance.fromMap(Map<String, dynamic> map) {
    return LoanBalance(
      map['balance'] as num,
    );
  }

  String toJson() => json.encode(toMap());

  factory LoanBalance.fromJson(String source) =>
      LoanBalance.fromMap(json.decode(source) as Map<String, dynamic>);

  @override
  String toString() => 'LoanBalance(balance: $balance)';

  @override
  bool operator ==(covariant LoanBalance other) {
    if (identical(this, other)) return true;

    return other.balance == balance;
  }

  @override
  int get hashCode => balance.hashCode;
}
