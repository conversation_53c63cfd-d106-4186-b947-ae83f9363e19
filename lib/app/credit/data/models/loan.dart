import 'dart:convert';

import 'package:equatable/equatable.dart';
import 'package:td_commons_flutter/utils/methods.dart';

class Loan extends Equatable {
  final String id;
  final String loanId;
  final String retailOutletId;
  final String createdBy;
  final num principal;
  final num balance;
  final String payChannel;
  final String serviceChannel;
  final String paymentMethod;
  final DateTime createdAt;
  final dynamic payeeDetails;

  const Loan(
    this.id,
    this.loanId,
    this.retailOutletId,
    this.createdBy,
    this.principal,
    this.balance,
    this.payChannel,
    this.serviceChannel,
    this.paymentMethod,
    this.createdAt,
    this.payeeDetails,
  );

  factory Loan.defaultValue() {
    return Loan(
      'id',
      'loanId',
      'retailOutletId',
      'createdBy',
      0,
      0,
      'payChannel',
      'serviceChannel',
      'paymentMethod',
      DateTime.now(),
      null,
    );
  }

  Loan copyWith({
    String? id,
    String? loanId,
    String? retailOutletId,
    String? createdBy,
    num? principal,
    num? balance,
    String? payChannel,
    String? serviceChannel,
    String? paymentMethod,
    DateTime? createdAt,
    dynamic payeeDetails,
  }) {
    return Loan(
      id ?? this.id,
      loanId ?? this.loanId,
      retailOutletId ?? this.retailOutletId,
      createdBy ?? this.createdBy,
      principal ?? this.principal,
      balance ?? this.balance,
      payChannel ?? this.payChannel,
      serviceChannel ?? this.serviceChannel,
      paymentMethod ?? this.paymentMethod,
      createdAt ?? this.createdAt,
      payeeDetails ?? this.payeeDetails,
    );
  }

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'id': id,
      'loanId': loanId,
      'retailOutletId': retailOutletId,
      'createdBy': createdBy,
      'principal': principal,
      'balance': balance,
      'payChannel': payChannel,
      'serviceChannel': serviceChannel,
      'paymentMethod': paymentMethod,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'payeeDetails': payeeDetails,
    };
  }

  factory Loan.fromMap(Map<String, dynamic> map) {
    return Loan(
      map['_id'] as String,
      map['loanId'] as String,
      map['retailOutletId'] as String,
      map['createdBy'] as String,
      map['principal'] as num,
      map['balance'] as num,
      map['payChannel'] as String,
      map['serviceChannel'] as String,
      map['paymentMethod'] as String,
      parseDate(map['createdAt'])!,
      map['payeeDetails'] as dynamic,
    );
  }

  String toJson() => json.encode(toMap());

  factory Loan.fromJson(String source) =>
      Loan.fromMap(json.decode(source) as Map<String, dynamic>);

  @override
  bool get stringify => true;

  @override
  List<Object> get props {
    return [
      id,
      loanId,
      retailOutletId,
      createdBy,
      principal,
      balance,
      payChannel,
      serviceChannel,
      paymentMethod,
      createdAt,
      payeeDetails,
    ];
  }
}
