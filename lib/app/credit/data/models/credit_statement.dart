import 'package:td_procurement/core/models/pagination.dart';

import 'loan.dart';

class CreditStatement {
  final List<Loan> loans;
  final num balance;
  final num totalCredits;
  final num totalDebits;
  final num totalCount;
  final Pagination pagination;
  final bool isCompleted;

  CreditStatement({
    required this.loans,
    required this.balance,
    required this.totalCredits,
    required this.totalDebits,
    required this.totalCount,
    required this.pagination,
    this.isCompleted = false,
  });
}
