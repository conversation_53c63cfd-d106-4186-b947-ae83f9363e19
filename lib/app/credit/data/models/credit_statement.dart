import 'package:td_procurement/core/models/pagination.dart';

import 'loan.dart';

class CreditStatement {
  final List<Loan> loans;
  final num balance;
  final num totalCredits;
  final num totalDebits;
  final num totalCount;
  final Pagination pagination;

  CreditStatement(
    this.loans,
    this.balance,
    this.totalCredits,
    this.totalDebits,
    this.totalCount,
    this.pagination,
  );
}
