import 'dart:convert';

import 'package:equatable/equatable.dart';
import 'package:td_commons_flutter/utils/index.dart';

class LoanRepayment extends Equatable {
  final num amount;
  final String narration;
  final DateTime createdAt;
  final String reference;
  // final String id;
  // final String retailOutletId;
  // final String loanId;
  // final num amount;
  // final String status;
  // final DateTime dueAt;
  // final DateTime date;
  // final num payment;
  // final num outstandingPrincipal;
  // final num interest;
  // final num principal;
  // final num earlyRepayment;
  // final DateTime createdAt;

  const LoanRepayment(
    this.amount,
    this.narration,
    this.createdAt,
    this.reference,
  );

  LoanRepayment copyWith({
    num? amount,
    String? narration,
    DateTime? createdAt,
    String? reference,
  }) {
    return LoanRepayment(
      amount ?? this.amount,
      narration ?? this.narration,
      createdAt ?? this.createdAt,
      reference ?? this.reference,
    );
  }

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'amount': amount,
      'narration': narration,
      'createdAt': createdAt.millisecondsSinceEpoch,
      'reference': reference,
    };
  }

  factory LoanRepayment.fromMap(Map<String, dynamic> map) {
    return LoanRepayment(
      map['amount'] as num,
      map['narration'] as String,
      parseDate(map['createdAt'])!,
      map['reference'] as String,
    );
  }

  String toJson() => json.encode(toMap());

  factory LoanRepayment.fromJson(String source) =>
      LoanRepayment.fromMap(json.decode(source) as Map<String, dynamic>);

  @override
  bool get stringify => true;

  @override
  List<Object> get props {
    return [
      amount,
      narration,
      createdAt,
      reference,
    ];
  }
}
