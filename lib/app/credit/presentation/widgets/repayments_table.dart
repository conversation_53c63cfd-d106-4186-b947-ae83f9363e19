import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:gap/gap.dart';
import 'package:number_pagination/number_pagination.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:td_procurement/app/credit/data/models/repayment.dart';
import 'package:td_procurement/app/credit/presentation/controllers/credit_controller.dart';
import 'package:td_procurement/src/components/error/failure_widget.dart';
import 'package:td_procurement/src/components/widgets/currency.dart';
import 'package:td_procurement/src/components/widgets/empty_widget.dart';
import 'package:td_procurement/src/extensions/date_time.dart';
import 'package:td_procurement/src/res/assets/svgs/svgs.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';
import 'package:td_procurement/src/res/values/constants/constants.dart';

class RepaymentsTableWidget extends ConsumerStatefulWidget {
  final List<LoanRepayment> repayments;
  final String? currency;
  final int total;

  const RepaymentsTableWidget(
    this.repayments,
    this.currency, {
    super.key,
    required this.total,
  });

  @override
  ConsumerState<RepaymentsTableWidget> createState() =>
      _RepaymentsTableWidgetState();
}

class _RepaymentsTableWidgetState extends ConsumerState<RepaymentsTableWidget> {
  @override
  Widget build(BuildContext context) {
    final repaymentsState =
        ref.watch(creditControllerProvider.select((state) => state.repayments));

    return repaymentsState.when(
      data: (data) {
        if (data.isEmpty) {
          return const EmptyWidget(
            icon: kReceiptSvg,
            title: 'No repayments',
            subTitle: 'There are no repayments matching your filter criteria',
          );
        }
        return Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Expanded(
              child: SingleChildScrollView(
                child: _TableContent(
                  repayments: data,
                  currency: widget.currency,
                ),
              ),
            ),
            const Gap(20),
            RepaymentsPagination(total: widget.total),
            const Gap(20),
          ],
        );
      },
      error: (error, __) => FailureWidget(
        e: error,
        retry: () {
          final controller = ref.read(creditControllerProvider.notifier);
          final params =
              ref.read(creditControllerProvider).creditStatementParams;
          if (params != null) {
            controller.fetchRepayments(params, forced: true);
          }
        },
      ),
      loading: () => const RepaymentsTableLoadingView(),
    );
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
  }
}

class RepaymentsTableLoadingView extends StatelessWidget {
  const RepaymentsTableLoadingView({super.key});

  @override
  Widget build(BuildContext context) {
    return Skeletonizer(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          Expanded(
            child: SingleChildScrollView(
              child: _TableContent(
                repayments: List.filled(
                    20,
                    LoanRepayment(
                        1000, 'Sample narration', DateTime.now(), 'REF123')),
              ),
            ),
          ),
          const Gap(20),
          const RepaymentsPagination(),
          const Gap(20),
        ],
      ),
    );
  }
}

class _TableContent extends StatelessWidget {
  final List<LoanRepayment> repayments;
  final String? currency;

  const _TableContent({
    required this.repayments,
    this.currency,
  });

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: Palette.kE7E7E7),
        borderRadius: BorderRadius.circular(8),
      ),
      child: DataTable(
        showCheckboxColumn: false,
        headingRowColor: WidgetStatePropertyAll(Palette.kF7F7F7),
        columns: [
          DataColumn(
            label: _buildHeaderCell('Reference', textTheme),
          ),
          DataColumn(
            label: _buildHeaderCell('Amount', textTheme),
          ),
          DataColumn(
            label: _buildHeaderCell('Narration', textTheme),
          ),
          DataColumn(
            label: _buildHeaderCell('Date', textTheme),
          ),
          const DataColumn(
            label: SizedBox.shrink(),
          ), // Empty space for consistency
        ],
        rows: repayments
            .map(
              (LoanRepayment element) => DataRow(
                onSelectChanged: (_) {},
                cells: [
                  DataCell(
                    _buildContentText(element.reference, textTheme),
                  ),
                  DataCell(
                    _buildContentAmount(
                        element.amount, textTheme, Palette.kE61010, currency),
                  ),
                  DataCell(
                    _buildContentText(element.narration, textTheme),
                  ),
                  DataCell(
                    _buildContentText(element.createdAt.toDate(), textTheme),
                  ),
                  const DataCell(
                    SizedBox.shrink(),
                  )
                ],
              ),
            )
            .toList(),
      ),
    );
  }

  Widget _buildHeaderCell(String text, TextTheme textTheme) {
    return Padding(
      padding: const EdgeInsets.only(left: 10),
      child: Text(
        text,
        style: textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w500),
      ),
    );
  }

  Widget _buildContentAmount(num amount, TextTheme textTheme,
      [Color? color, String? currency]) {
    return Padding(
      padding: const EdgeInsets.all(10),
      child: CurrencyWidget(
        amount,
        currency ?? kDefaultCurrency,
        amountStyle: textTheme.bodyMedium?.copyWith(
          color: color,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildContentText(String text, TextTheme textTheme) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 12),
      child: Text(
        text,
        style: textTheme.bodyMedium?.copyWith(
          color: Palette.blackSecondary,
        ),
      ),
    );
  }
}

class RepaymentsPagination extends ConsumerWidget {
  final int? total;

  const RepaymentsPagination({super.key, this.total});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final params = ref.watch(creditControllerProvider
        .select((state) => state.creditStatementParams));

    if (params == null || (total ?? 0) <= params.limit) {
      return const SizedBox.shrink();
    }

    final totalPages = ((total ?? 0) / params.limit).ceil();

    return Align(
      alignment: Alignment.topRight,
      child: SizedBox(
        width: 500,
        child: NumberPagination(
          onPageChanged: (int pageNumber) {
            ref
                .read(creditControllerProvider.notifier)
                .paginateData(pageNumber);
          },
          visiblePagesCount: totalPages > 5 || totalPages == 0 ? 5 : totalPages,
          buttonElevation: 0.3,
          totalPages: totalPages,
          currentPage: params.batch,
          buttonRadius: 8,
          selectedButtonColor: Palette.primaryBlack,
          selectedNumberColor: Colors.white,
          unSelectedButtonColor: Colors.white,
          unSelectedNumberColor: Palette.blackSecondary,
          fontSize: 14,
        ),
      ),
    );
  }
}
