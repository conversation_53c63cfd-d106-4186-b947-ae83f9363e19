import 'dart:async';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:td_procurement/app/auth/presentation/controllers/session_controller.dart';
import 'package:td_procurement/app/credit/data/models/credit_statement.dart';
import 'package:td_procurement/app/credit/domain/entities/credit_statement_params.dart';
import 'package:td_procurement/app/credit/domain/use_cases/credit_use_cases.dart';
import 'package:td_procurement/app/credit/presentation/controllers/credit_state.dart';

final creditControllerProvider =
    NotifierProvider<CreditController, CreditState>(() => CreditController());

final creditStatementArgProvider =
    StateProvider<CreditStatementParams?>((ref) => null);

class CreditController extends Notifier<CreditState> {
  @override
  CreditState build() {
    final session = ref.watch(sessionController);

    // If accessToken is null or empty (logged out), return initial state
    if (session.accessToken == null || session.accessToken!.isEmpty) {
      return CreditState.initial();
    }

    return CreditState.initial();
  }

  Future<void> fetchCreditStatement(
    CreditStatementParams params, {
    int retryCount = 2,
    int delaySeconds = 3,
    bool forced = false,
  }) async {
    int attempts = 0;
    bool shouldRetry = true;

    // Update params in state
    state = state.copyWith(creditStatementParams: params);

    if (forced) {
      state = state.copyWith(creditStatement: const AsyncLoading());
    } else {
      if (state.creditStatement.hasValue &&
          state.creditStatement.value != null &&
          state.creditStatement.value!.isCompleted) {
        return;
      }
      state = state.copyWith(creditStatement: const AsyncLoading());
    }

    while (attempts <= retryCount && shouldRetry) {
      attempts++;

      final res = await ref.read(creditUseCaseProvider(params));

      res.when(
        success: (data) {
          state = state.copyWith(
            creditStatement: AsyncData(data),
          );
          shouldRetry = false;
        },
        failure: (e, _) async {
          if (attempts > retryCount) {
            state = state.copyWith(
              creditStatement: AsyncError(e, StackTrace.current),
            );
            shouldRetry = false;
          } else {
            await Future.delayed(Duration(seconds: delaySeconds));
          }
        },
      );
    }
  }

  Future<void> fetchRepayments(
    CreditStatementParams params, {
    int retryCount = 2,
    int delaySeconds = 3,
    bool forced = false,
  }) async {
    int attempts = 0;
    bool shouldRetry = true;

    // Update params in state
    state = state.copyWith(creditStatementParams: params);

    if (forced) {
      state = state.copyWith(repayments: const AsyncLoading());
    } else {
      if (state.repayments.hasValue &&
          state.repayments.value != null &&
          state.repayments.value!.isNotEmpty) {
        return;
      }
      state = state.copyWith(repayments: const AsyncLoading());
    }

    while (attempts <= retryCount && shouldRetry) {
      attempts++;

      final res = await ref.read(fetchRepaymentsUseCaseProvider(params));

      res.when(
        success: (data) {
          state = state.copyWith(
            repayments: AsyncData(data),
          );
          shouldRetry = false;
        },
        failure: (e, _) async {
          if (attempts > retryCount) {
            state = state.copyWith(
              repayments: AsyncError(e, StackTrace.current),
            );
            shouldRetry = false;
          } else {
            await Future.delayed(Duration(seconds: delaySeconds));
          }
        },
      );
    }
  }

  void updatePaymentType(CreditPaymentType paymentType) {
    //     final currentParams = state.creditStatementParams;
    // final newParams = currentParams?.payment(paymentType) ??
    //     CreditStatementParams(paymentType: paymentType);
    final newParams = CreditStatementParams(paymentType: paymentType);

    // Update the global state provider as well for backward compatibility
    ref.read(creditStatementArgProvider.notifier).state = newParams;

    // Fetch appropriate data based on payment type
    if (paymentType == CreditPaymentType.debits) {
      fetchRepayments(newParams);
    } else {
      fetchCreditStatement(newParams);
    }
  }

  void updateDateFilter(DateTime? startDate, DateTime? endDate) {
    final currentParams = state.creditStatementParams;
    if (currentParams != null) {
      final newParams = CreditStatementParams(
        batch: currentParams.batch,
        limit: currentParams.limit,
        sortBy: currentParams.sortBy,
        paymentType: currentParams.paymentType,
        startDate: startDate,
        endDate: endDate,
      );

      // Update the global state provider as well
      ref.read(creditStatementArgProvider.notifier).state = newParams;

      // Fetch appropriate data based on payment type
      if (newParams.paymentType == CreditPaymentType.debits) {
        fetchRepayments(newParams);
      } else {
        fetchCreditStatement(newParams);
      }
    }
  }

  void paginateData(int page) {
    final currentParams = state.creditStatementParams;
    if (currentParams != null) {
      final newParams = currentParams.paginate(page);

      // Update the global state provider as well
      ref.read(creditStatementArgProvider.notifier).state = newParams;

      // Fetch appropriate data based on payment type
      if (newParams.paymentType == CreditPaymentType.debits) {
        fetchRepayments(newParams);
      } else {
        fetchCreditStatement(newParams);
      }
    }
  }
}

// Legacy controller for backward compatibility
class CreditTableController extends AutoDisposeFamilyAsyncNotifier<
    CreditStatement?, CreditStatementParams?> {
  @override
  FutureOr<CreditStatement?> build(arg) async {
    return arg != null
        ? (await ref.read(
            creditUseCaseProvider(arg),
          ))
            .extract()
        : null;
  }
}

final creditTableControllerProvider = AutoDisposeAsyncNotifierProviderFamily<
    CreditTableController, CreditStatement?, CreditStatementParams?>(() {
  return CreditTableController();
});
