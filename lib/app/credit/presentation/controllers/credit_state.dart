import 'package:equatable/equatable.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:td_procurement/app/credit/data/models/credit_statement.dart';
import 'package:td_procurement/app/credit/data/models/repayment.dart';
import 'package:td_procurement/app/credit/domain/entities/credit_statement_params.dart';

@immutable
class CreditState extends Equatable {
  final AsyncValue<CreditStatement?> creditStatement;
  final AsyncValue<List<LoanRepayment>> repayments;
  final CreditStatementParams? creditStatementParams;

  const CreditState({
    required this.creditStatement,
    required this.repayments,
    this.creditStatementParams,
  });

  factory CreditState.initial() {
    return const CreditState(
      creditStatement: AsyncData(null),
      repayments: AsyncData([]),
      creditStatementParams: null,
    );
  }

  CreditState copyWith({
    AsyncValue<CreditStatement>? creditStatement,
    AsyncValue<List<LoanRepayment>>? repayments,
    CreditStatementParams? creditStatementParams,
  }) {
    return CreditState(
      creditStatement: creditStatement ?? this.creditStatement,
      repayments: repayments ?? this.repayments,
      creditStatementParams:
          creditStatementParams ?? this.creditStatementParams,
    );
  }

  @override
  List<Object?> get props => [
        creditStatement,
        repayments,
        creditStatementParams,
      ];
}
