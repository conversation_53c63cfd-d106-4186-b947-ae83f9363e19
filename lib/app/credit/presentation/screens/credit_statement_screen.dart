import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:gap/gap.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:td_procurement/app/auth/presentation/controllers/user_controller.dart';
import 'package:td_procurement/app/credit/domain/entities/credit_statement_params.dart';
import 'package:td_procurement/app/credit/domain/use_cases/credit_use_cases.dart';
import 'package:td_procurement/app/credit/presentation/controllers/credit_controller.dart';
import 'package:td_procurement/app/credit/presentation/widgets/credit_card.dart';
import 'package:td_procurement/app/credit/presentation/widgets/credit_filter.dart';
import 'package:td_procurement/app/credit/presentation/widgets/credit_table.dart';
import 'package:td_procurement/src/components/buttons/custom_elevated_button.dart';
import 'package:td_procurement/src/components/error/failure_widget.dart';
import 'package:td_procurement/src/components/toast/toast.dart';
import 'package:td_procurement/src/res/assets/svgs/svgs.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';
import 'package:td_procurement/src/utils/helpers/providers.dart';

class CreditStatementScreen extends ConsumerStatefulWidget {
  const CreditStatementScreen({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() {
    return _CreditStatementScreen();
  }
}

class _CreditStatementScreen extends ConsumerState<CreditStatementScreen> {
  late final user = ref.read(userControllerProvider);

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final currency = ref.read(currencyCodeProvider);
    final creditState = ref.watch(
      creditStatementControllerProvider,
    );
    return creditState.when(
      data: (data) {
        final List<(num, CreditPaymentType)> cards = [
          (data.balance, CreditPaymentType.balance),
          (data.totalCredits, CreditPaymentType.credits),
          (data.totalDebits, CreditPaymentType.debits),
        ];
        return Column(
          children: [
            Padding(
              padding: const EdgeInsets.only(
                  left: 30, top: 20, bottom: 20, right: 15),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('Credit Statement', style: textTheme.headlineMedium),
                  const Gap(20),
                  Row(
                    children: cards
                        .map(
                          (element) => CreditCard(
                            element.$1,
                            element.$2,
                            currency: currency,
                          ),
                        )
                        .toList(),
                  ),
                  const Gap(20),
                  Row(
                    children: [
                      const CreditFilter(),
                      const Spacer(),
                      CustomElevatedButton(
                        text: 'Send to Email',
                        icon: kMailSvg,
                        onPressed: () => sendEmail(data.pagination.page),
                        size: const Size(133, 38),
                      ),
                      const Gap(10),
                    ],
                  ),
                ],
              ),
            ),
            Divider(thickness: 1, color: Palette.kE7E7E7, height: 1),
            Expanded(
              child: CreditTableWidget(
                data.loans,
                currency,
                total: data.totalCount.toInt(),
              ),
            ),
          ],
        );
      },
      error: (error, _) => FailureWidget(
        e: error,
        retry: () => ref.invalidate(creditStatementControllerProvider),
      ),
      loading: () => const CreditLoadingView(),
    );
  }

  void sendEmail(int page) async {
    Toast.info(
        'Please hold on while we process and send this credit statement to ${user?.email ?? user?.currentRetailOutlet?.email ?? 'you'}',
        context,
        title: "Processing...",
        duration: 3);
    final response = await ref.read(
      sendCreditUseCaseProvider(
        ref.read(creditStatementArgProvider)?.email() ??
            CreditStatementParams().email(),
      ),
    );
    response.when(success: (_) {
      if (mounted) {
        Toast.success(
            "Your credit statement has been sent to ${user?.email ?? user?.currentRetailOutlet?.email ?? 'you'}",
            context,
            title: 'Successful!');
      }
    }, failure: (error, _) {
      if (mounted) Toast.apiError(error, context);
    });
  }
}

class CreditLoadingView extends StatelessWidget {
  const CreditLoadingView({super.key});

  @override
  Widget build(BuildContext context) {
    return Skeletonizer(
      child: Column(
        children: [
          Padding(
            padding:
                const EdgeInsets.only(left: 30, top: 20, bottom: 20, right: 15),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text('Credit Statement'),
                const Gap(20),
                Row(
                  children: List.filled(
                    3,
                    const CreditCard(0, CreditPaymentType.credits),
                  ),
                ),
                const Gap(20),
                Row(
                  children: [
                    const CreditFilter(),
                    const Spacer(),
                    CustomElevatedButton(
                      text: 'Send to Email',
                      icon: kMailSvg,
                      onPressed: () {},
                      size: const Size(133, 38),
                    ),
                    const Gap(10),
                  ],
                ),
              ],
            ),
          ),
          Divider(thickness: 1, color: Palette.kE7E7E7, height: 1),
          const Expanded(
            child: CreditTableLoadingView(),
          ),
        ],
      ),
    );
  }
}
