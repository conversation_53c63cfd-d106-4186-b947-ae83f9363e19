import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:gap/gap.dart';
import 'package:skeletonizer/skeletonizer.dart';
import 'package:td_procurement/app/auth/presentation/controllers/user_controller.dart';
import 'package:td_procurement/app/credit/data/models/credit_statement.dart';
import 'package:td_procurement/app/credit/domain/entities/credit_statement_params.dart';
import 'package:td_procurement/app/credit/domain/use_cases/credit_use_cases.dart';
import 'package:td_procurement/app/credit/presentation/controllers/credit_controller.dart';
import 'package:td_procurement/app/credit/presentation/controllers/credit_state.dart';
import 'package:td_procurement/app/credit/presentation/widgets/credit_card.dart';
import 'package:td_procurement/app/credit/presentation/widgets/credit_filter.dart';
import 'package:td_procurement/app/credit/presentation/widgets/credit_table.dart';
import 'package:td_procurement/app/credit/presentation/widgets/repayments_table.dart';
import 'package:td_procurement/src/components/buttons/custom_elevated_button.dart';
import 'package:td_procurement/src/components/error/failure_widget.dart';
import 'package:td_procurement/src/components/toast/toast.dart';
import 'package:td_procurement/src/extensions/index.dart';
import 'package:td_procurement/src/res/assets/svgs/svgs.dart';
import 'package:td_procurement/src/res/styles/colors/colors.dart';
import 'package:td_procurement/src/utils/helpers/providers.dart';

class CreditStatementScreen extends ConsumerStatefulWidget {
  const CreditStatementScreen({super.key});

  @override
  ConsumerState<ConsumerStatefulWidget> createState() {
    return _CreditStatementScreen();
  }
}

class _CreditStatementScreen extends ConsumerState<CreditStatementScreen> {
  late final user = ref.read(userControllerProvider);

  @override
  void initState() {
    super.initState();
    // Initialize with default parameters
    Future.microtask(() {
      final controller = ref.read(creditControllerProvider.notifier);
      controller.fetchCreditStatement(CreditStatementParams());
    });
  }

  @override
  Widget build(BuildContext context) {
    final creditState = ref.watch(creditControllerProvider);
    final creditStatement = creditState.creditStatement;

    return creditStatement.when(
      data: (data) => _buildDataWidget(data, creditState),
      error: (error, _) => _buildDataWidget(null, creditState, error),
      loading: () => const CreditLoadingView(),
    );
  }

  Widget _buildDataWidget(CreditStatement? data, CreditState creditState,
      [Object? error]) {
    final currency = ref.read(currencyCodeProvider);
    final currentPaymentType = creditState.creditStatementParams?.paymentType ??
        CreditPaymentType.balance;

    final List<(num, CreditPaymentType)> cards = [
      (data?.balance ?? 0, CreditPaymentType.balance),
      (data?.totalCredits ?? 0, CreditPaymentType.credits),
      (data?.totalDebits ?? 0, CreditPaymentType.debits),
    ];

    return Column(
      children: [
        Padding(
          padding:
              const EdgeInsets.only(left: 30, top: 20, bottom: 20, right: 15),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('Credit Statement', style: context.textTheme.headlineMedium),
              const Gap(20),
              Row(
                children: cards
                    .map(
                      (element) => CreditCard(
                        element.$1,
                        element.$2,
                        currency: currency,
                      ),
                    )
                    .toList(),
              ),
              const Gap(20),
              Row(
                children: [
                  const CreditFilter(),
                  const Spacer(),
                  CustomElevatedButton(
                    text: 'Send to Email',
                    icon: kMailSvg,
                    onPressed: () => sendEmail(),
                    size: const Size(133, 38),
                  ),
                  const Gap(10),
                ],
              ),
            ],
          ),
        ),
        Divider(thickness: 1, color: Palette.kE7E7E7, height: 1),
        Expanded(
          child: error != null && data == null
              ? FailureWidget(
                  e: error,
                  retry: () {
                    final controller =
                        ref.read(creditControllerProvider.notifier);
                    final params = creditState.creditStatementParams ??
                        CreditStatementParams();
                    if (params.paymentType == CreditPaymentType.debits) {
                      controller.fetchRepayments(params, forced: true);
                    } else {
                      controller.fetchCreditStatement(params, forced: true);
                    }
                  },
                )
              : _buildTableWidget(currentPaymentType, creditState, currency),
        ),
      ],
    );
  }

  Widget _buildTableWidget(
    CreditPaymentType paymentType,
    CreditState creditState,
    String currency,
  ) {
    if (paymentType == CreditPaymentType.debits) {
      // Show repayments table for Total Repayment card
      final repayments = creditState.repayments;
      return repayments.when(
        data: (repayments) => RepaymentsTableWidget(
          repayments,
          currency,
          total: repayments
              .length, // You might want to get this from a different source
        ),
        error: (error, _) => FailureWidget(
          e: error,
          retry: () {
            final controller = ref.read(creditControllerProvider.notifier);
            final params =
                ref.read(creditControllerProvider).creditStatementParams ??
                    CreditStatementParams();
            controller.fetchRepayments(params, forced: true);
          },
        ),
        loading: () => const RepaymentsTableLoadingView(),
      );
    } else {
      // Show loans table for Balance and Total Credit cards
      final data = creditState.creditStatement.value!;
      return CreditTableWidget(
        data.loans,
        currency,
        total: data.totalCount.toInt(),
      );
    }
  }

  void sendEmail() async {
    Toast.info(
        'Please hold on while we process and send this credit statement to ${user?.email ?? user?.currentRetailOutlet?.email ?? 'you'}',
        context,
        title: "Processing...",
        duration: 3);
    final params = ref.read(creditControllerProvider).creditStatementParams ??
        CreditStatementParams();
    final response = await ref.read(
      sendCreditUseCaseProvider(params.email()),
    );
    response.when(success: (_) {
      if (mounted) {
        Toast.success(
            "Your credit statement has been sent to ${user?.email ?? user?.currentRetailOutlet?.email ?? 'you'}",
            context,
            title: 'Successful!');
      }
    }, failure: (error, _) {
      if (mounted) Toast.apiError(error, context);
    });
  }
}

class CreditLoadingView extends StatelessWidget {
  const CreditLoadingView({super.key});

  @override
  Widget build(BuildContext context) {
    return Skeletonizer(
      enabled: true,
      child: Column(
        children: [
          Padding(
            padding:
                const EdgeInsets.only(left: 30, top: 20, bottom: 20, right: 15),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text('Credit Statement'),
                const Gap(20),
                Row(
                  children: List.filled(
                    3,
                    const CreditCard(0, CreditPaymentType.credits),
                  ),
                ),
                const Gap(20),
                Row(
                  children: [
                    const CreditFilter(),
                    const Spacer(),
                    CustomElevatedButton(
                      text: 'Send to Email',
                      icon: kMailSvg,
                      onPressed: () {},
                      size: const Size(133, 38),
                    ),
                    const Gap(10),
                  ],
                ),
              ],
            ),
          ),
          Divider(thickness: 1, color: Palette.kE7E7E7, height: 1),
          const Expanded(
            child: CreditTableLoadingView(),
          ),
        ],
      ),
    );
  }
}
