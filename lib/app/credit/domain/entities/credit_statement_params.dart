import 'dart:ui';

class CreditStatementParams {
  final int batch;
  final int limit;
  // final CreditStatementType type;
  final CreditStatementSorting sortBy;
  final CreditPaymentType? paymentType;
  final DateTime? startDate;
  final DateTime? endDate;

  CreditStatementParams({
    this.batch = 1,
    this.limit = 10,
    // this.type = CreditStatementType.list,
    this.sortBy = CreditStatementSorting.desc,
    this.paymentType = CreditPaymentType.balance,
    this.endDate,
    this.startDate,
  });

  CreditStatementParams email() => CreditStatementParams(
        // type: CreditStatementType.email,
        startDate: startDate,
        endDate: endDate,
        paymentType: paymentType,
      );

  CreditStatementParams paginate(int index) => CreditStatementParams(
        batch: index,
        // type: type,
        paymentType: paymentType,
        limit: limit,
        sortBy: sortBy,
        startDate: startDate,
        endDate: endDate,
      );

  CreditStatementParams sort(CreditStatementSorting sort) =>
      CreditStatementParams(
        batch: batch,
        // type: type,
        limit: limit,
        sortBy: sort,
        paymentType: paymentType,
        startDate: startDate,
        endDate: endDate,
      );

  CreditStatementParams payment(CreditPaymentType paymentType) =>
      CreditStatementParams(
        batch: batch,
        // type: type,
        limit: limit,
        sortBy: sortBy,
        paymentType: paymentType,
        startDate: startDate,
        endDate: endDate,
      );

  Map<String, Object?> toMap() {
    return {
      'batch': batch,
      'limit': limit,
      // 'type': type.name,
      'sortBy': sortBy.name,
      if (paymentType != CreditPaymentType.balance)
        'paymentType': paymentType?.name,
      if (startDate != null && endDate != null) ...{
        'startDate': startDate!.toIso8601String(),
        'endDate': endDate!.toIso8601String(),
      }
    };
  }
}

// enum CreditStatementType { list, email }

enum CreditStatementSorting { desc, asc }

enum CreditPaymentType {
  balance(
    'Balance',
    Color.fromRGBO(8, 31, 36, 1),
  ),
  credits(
    'Total Credit',
    Color.fromRGBO(12, 166, 83, 1),
  ),
  debits(
    'Total Repayment',
    Color.fromRGBO(230, 16, 16, 1),
  );

  const CreditPaymentType(this.title, this.color);
  final String title;
  final Color color;
}

extension CreditSortLabel on CreditStatementSorting {
  get label => switch (this) {
        CreditStatementSorting.desc => 'Descending',
        CreditStatementSorting.asc => 'Ascending',
      };
}
