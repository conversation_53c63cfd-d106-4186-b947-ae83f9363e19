import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:td_procurement/app/credit/data/models/balance.dart';
import 'package:td_procurement/app/credit/data/models/credit_statement.dart';
import 'package:td_procurement/app/credit/data/models/repayment.dart';
import 'package:td_procurement/app/credit/data/repositories/credit_repo.dart';
import 'package:td_procurement/app/credit/domain/entities/credit_statement_params.dart';
import 'package:td_procurement/core/DI/di_providers.dart';
import 'package:td_procurement/core/services/api/api_response.dart';

final creditUseCaseProvider = AutoDisposeProviderFamily<
    Future<ApiResponse<CreditStatement>>, CreditStatementParams>(
  (ref, arg) => UseCase<CreditStatement>().call(
    () => ref.read(creditRepoProvider).fetchCreditStatement(arg),
  ),
);

final loanBalanceUseCaseProvider =
    AutoDisposeProviderFamily<Future<ApiResponse<LoanBalance>>, dynamic>(
  (ref, arg) => UseCase<LoanBalance>().call(
    () => ref.read(creditRepoProvider).fetchLoanBalance(),
  ),
);

final sendCreditUseCaseProvider =
    AutoDisposeProviderFamily<Future<ApiResponse<bool>>, CreditStatementParams>(
  (ref, arg) => UseCase<bool>().call(
    () => ref.read(creditRepoProvider).sendCreditStatement(arg),
  ),
);

final fetchRepaymentsUseCaseProvider = AutoDisposeProviderFamily<
    Future<ApiResponse<List<LoanRepayment>>>, CreditStatementParams>(
  (ref, arg) => UseCase<List<LoanRepayment>>().call(
    () => ref.read(creditRepoProvider).fetchRepayments(arg),
  ),
);
